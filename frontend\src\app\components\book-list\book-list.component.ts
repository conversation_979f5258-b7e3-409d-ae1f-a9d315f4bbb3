import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatDialogModule, MatDialog } from "@angular/material/dialog";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatMenuModule } from "@angular/material/menu";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatDividerModule } from "@angular/material/divider";
import { FormsModule } from "@angular/forms";
import { SelectionModel } from "@angular/cdk/collections";
import { Book } from "../../models/book.model";
import { BookService } from "../../services/book.service";
import { AuthService } from "../../services/auth.service";

@Component({
  selector: "app-book-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatDialogModule,
    MatCheckboxModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    FormsModule,
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Danh sách Sách</mat-card-title>
        <div class="spacer"></div>
        <div class="header-actions">
          <!-- Bulk Actions -->
          <div class="bulk-actions" *ngIf="selection.hasValue() && canManageBooks()">
            <span class="selected-count">
              Đã chọn {{ selection.selected.length }} sách
            </span>
            <button
              mat-raised-button
              color="warn"
              (click)="deleteSelectedBooks()"
              [disabled]="isDeleting"
            >
              <mat-icon>delete</mat-icon>
              Xóa đã chọn
            </button>
          </div>
          
          <!-- Main Actions -->
          <button
            *ngIf="canManageBooks()"
            mat-button
            [matMenuTriggerFor]="bulkMenu"
            color="accent"
          >
            <mat-icon>more_vert</mat-icon>
            Thao tác hàng loạt
          </button>
          
          <button
            *ngIf="canManageBooks()"
            mat-raised-button
            color="primary"
            routerLink="/books/add"
          >
            <mat-icon>add</mat-icon>
            Thêm sách mới
          </button>
        </div>
      </mat-card-header>

      <mat-card-content>
        <!-- Unified Search -->
        <div class="search-section">
          <mat-form-field class="search-field">
            <mat-label>Tìm kiếm sách</mat-label>
            <input
              matInput
              [(ngModel)]="searchTerm"
              (input)="onSearch()"
              placeholder="Nhập tên sách, tác giả, hoặc ISBN..."
            />
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
          
          <button
            mat-icon-button
            (click)="clearSearch()"
            *ngIf="searchTerm"
            matTooltip="Xóa tìm kiếm"
          >
            <mat-icon>clear</mat-icon>
          </button>
        </div>

        <!-- Books table -->
        <div class="table-container">
          <table mat-table [dataSource]="books" class="mat-elevation-z8">
            <!-- Checkbox Column -->
            <ng-container matColumnDef="select" *ngIf="canManageBooks()">
              <th mat-header-cell *matHeaderCellDef>
                <mat-checkbox
                  (change)="$event ? masterToggle() : null"
                  [checked]="selection.hasValue() && isAllSelected()"
                  [indeterminate]="selection.hasValue() && !isAllSelected()"
                  matTooltip="Chọn tất cả"
                >
                </mat-checkbox>
              </th>
              <td mat-cell *matCellDef="let book">
                <mat-checkbox
                  (click)="$event.stopPropagation()"
                  (change)="$event ? selection.toggle(book) : null"
                  [checked]="selection.isSelected(book)"
                >
                </mat-checkbox>
              </td>
            </ng-container>

            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Tên sách</th>
              <td mat-cell *matCellDef="let book">
                <div class="book-info">
                  <strong>{{ book.title }}</strong>
                  <small *ngIf="book.isbn" class="isbn">ISBN: {{ book.isbn }}</small>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="author">
              <th mat-header-cell *matHeaderCellDef>Tác giả</th>
              <td mat-cell *matCellDef="let book">{{ book.author }}</td>
            </ng-container>

            <ng-container matColumnDef="categoryName">
              <th mat-header-cell *matHeaderCellDef>Thể loại</th>
              <td mat-cell *matCellDef="let book">{{ book.categoryName }}</td>
            </ng-container>

            <ng-container matColumnDef="quantity">
              <th mat-header-cell *matHeaderCellDef>Nhập về</th>
              <td mat-cell *matCellDef="let book">{{ book.quantity }}</td>
            </ng-container>

            <ng-container matColumnDef="availableQuantity">
              <th mat-header-cell *matHeaderCellDef>Trong kho</th>
              <td mat-cell *matCellDef="let book">
                {{ book.availableQuantity }}
              </td>
            </ng-container>
            
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Thao tác</th>
              <td mat-cell *matCellDef="let book">
                <button
                  *ngIf="canManageBooks()"
                  mat-icon-button
                  [routerLink]="['/books/edit', book.id]"
                  color="primary"
                  matTooltip="Chỉnh sửa"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  *ngIf="canManageBooks()"
                  mat-icon-button
                  (click)="deleteBook(book)"
                  color="warn"
                  matTooltip="Xóa"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>

        <div *ngIf="books.length === 0" class="no-data">
          <mat-icon>book</mat-icon>
          <p>{{ searchTerm ? 'Không tìm thấy sách nào phù hợp.' : 'Chưa có sách nào.' }}</p>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Bulk Actions Menu -->
    <mat-menu #bulkMenu="matMenu">
      <button mat-menu-item (click)="selectAll()">
        <mat-icon>select_all</mat-icon>
        <span>Chọn tất cả</span>
      </button>
      <button mat-menu-item (click)="clearSelection()">
        <mat-icon>clear</mat-icon>
        <span>Bỏ chọn tất cả</span>
      </button>
      <mat-divider></mat-divider>
      <button 
        mat-menu-item 
        (click)="deleteAllBooks()" 
        [disabled]="books.length === 0"
        class="warn-action"
      >
        <mat-icon>delete_sweep</mat-icon>
        <span>Xóa tất cả sách</span>
      </button>
    </mat-menu>
  `,
  styles: [
    `
      .search-section {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
      }

      .search-field {
        flex: 1;
        max-width: 500px;
      }

      .table-container {
        overflow-x: auto;
      }

      .mat-mdc-table {
        width: 100%;
      }

      .no-data {
        text-align: center;
        padding: 40px;
        color: #666;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
      }

      .no-data mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #ccc;
      }

      mat-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }

      .spacer {
        flex: 1 1 auto;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .bulk-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 16px;
        background-color: #e3f2fd;
        border-radius: 8px;
        border: 1px solid #2196f3;
      }

      .selected-count {
        font-size: 14px;
        color: #1976d2;
        font-weight: 500;
      }

      .book-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .isbn {
        color: #666;
        font-size: 12px;
      }

      .warn-action {
        color: #f44336 !important;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .header-actions {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
        }

        .bulk-actions {
          flex-direction: column;
          text-align: center;
        }
      }
    `,
  ],
})
export class BookListComponent implements OnInit {
  books: Book[] = [];
  displayedColumns: string[] = [];
  searchTerm: string = "";
  selection = new SelectionModel<Book>(true, []);
  isDeleting = false;

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.updateDisplayedColumns();
  }

  ngOnInit(): void {
    this.loadBooks();
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  private updateDisplayedColumns(): void {
    this.displayedColumns = this.canManageBooks() 
      ? ["select", "title", "author", "categoryName", "quantity", "availableQuantity", "actions"]
      : ["title", "author", "categoryName", "quantity", "availableQuantity"];
  }

  loadBooks(): void {
    this.bookService.getBooks().subscribe({
      next: (books) => {
        this.books = books;
        this.selection.clear();
      },
      error: (error) => {
        this.snackBar.open("Lỗi khi tải danh sách sách", "Đóng", {
          duration: 3000,
        });
      },
    });
  }

  onSearch(): void {
    if (this.searchTerm.trim()) {
      // Sử dụng tìm kiếm tổng hợp
      this.bookService.searchBooksGeneral(this.searchTerm.trim()).subscribe({
        next: (books) => {
          this.books = books;
          this.selection.clear();
        },
        error: (error) => {
          this.snackBar.open("Lỗi khi tìm kiếm sách", "Đóng", {
            duration: 3000,
          });
        },
      });
    } else {
      this.loadBooks();
    }
  }

  clearSearch(): void {
    this.searchTerm = "";
    this.loadBooks();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.books.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.books.forEach(book => this.selection.select(book));
  }

  selectAll(): void {
    this.books.forEach(book => this.selection.select(book));
  }

  clearSelection(): void {
    this.selection.clear();
  }

  // Delete methods
  deleteBook(book: Book): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${book.title}"?`)) {
      this.bookService.deleteBook(book.id).subscribe({
        next: () => {
          this.snackBar.open("Xóa sách thành công", "Đóng", {
            duration: 3000,
          });
          this.loadBooks();
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error) {
            if (typeof error.error === "string") {
              message = error.error;
            } else if (error.error.message) {
              message = error.error.message;
            } else if (error.error.title) {
              message = error.error.title;
            }
          } else if (error.message) {
            message = error.message;
          }

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
        },
      });
    }
  }

  deleteSelectedBooks(): void {
    const selectedBooks = this.selection.selected;
    const bookTitles = selectedBooks.map(book => book.title).join(', ');
    
    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedBooks.length} sách đã chọn?\n\n${bookTitles}`)) {
      this.isDeleting = true;
      const bookIds = selectedBooks.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(bookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }

  deleteAllBooks(): void {
    if (this.books.length === 0) {
      this.snackBar.open("Không có sách nào để xóa", "Đóng", {
        duration: 3000,
      });
      return;
    }

    const confirmMessage = `⚠️ CẢNH BÁO: Bạn đang chuẩn bị xóa TẤT CẢ ${this.books.length} sách!\n\nHành động này không thể hoàn tác. Bạn có chắc chắn muốn tiếp tục?`;
    
    if (confirm(confirmMessage)) {
      this.isDeleting = true;
      const allBookIds = this.books.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(allBookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }
}
