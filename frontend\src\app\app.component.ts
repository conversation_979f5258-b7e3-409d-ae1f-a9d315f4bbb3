import { Component, OnInit, ViewChild } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  RouterOutlet,
  RouterModule,
  Router,
  NavigationEnd,
} from "@angular/router";
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatSidenavModule, MatSidenav } from "@angular/material/sidenav";
import { MatListModule } from "@angular/material/list";
import { MatMenuModule } from "@angular/material/menu";
import { MatDividerModule } from "@angular/material/divider";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { AuthService } from "./services/auth.service";
import { User } from "./models/auth.model";
import { NotAuthenticatedComponent } from "./components/not-authenticated/not-authenticated.component";
import { HasPermissionDirective } from "./directives/has-permission.directive";
import { filter } from "rxjs";

@Component({
  selector: "app-root",
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatSidenavModule,
    MatListModule,
    MatMenuModule,
    MatDividerModule,
    MatSnackBarModule,
    NotAuthenticatedComponent,
    HasPermissionDirective,
  ],
  template: `
    <mat-toolbar color="primary">
      <button
        mat-icon-button
        (click)="toggleSidenav()"
        *ngIf="currentUser && showMainLayout"
      >
        <mat-icon>menu</mat-icon>
      </button>
      <span>Hệ thống Quản lý Thư viện</span>
      <span class="spacer"></span>

      <div *ngIf="currentUser; else loginButton">
        <button mat-button [matMenuTriggerFor]="userMenu">
          <mat-icon>account_circle</mat-icon>
          {{ currentUser.fullName }}
          <mat-icon>arrow_drop_down</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item disabled>
            <mat-icon>email</mat-icon>
            {{ currentUser.email }}
          </button>
          <button mat-menu-item disabled>
            <mat-icon>badge</mat-icon>
            {{ currentUser.role }}
          </button>
          <button mat-menu-item disabled *ngIf="!currentUser.emailVerified">
            <mat-icon>warning</mat-icon>
            Email chưa xác nhận
          </button>
          <button
            mat-menu-item
            *ngIf="!currentUser.emailVerified"
            (click)="showDebugInfo()"
          >
            <mat-icon>bug_report</mat-icon>
            Debug Info
          </button>
          <button
            mat-menu-item
            *ngIf="!currentUser.emailVerified"
            (click)="resendVerificationEmail()"
          >
            <mat-icon>refresh</mat-icon>
            Gửi lại email xác nhận
          </button>
          <button
            mat-menu-item
            *ngIf="!currentUser.emailVerified"
            (click)="syncWithToken()"
          >
            <mat-icon>sync</mat-icon>
            Sync với Token
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            Đăng xuất
          </button>
        </mat-menu>
      </div>

      <ng-template #loginButton>
        <button mat-button routerLink="/login" *ngIf="!showMainLayout">
          <mat-icon>login</mat-icon>
          Đăng nhập
        </button>
      </ng-template>
    </mat-toolbar>

    <!-- Main App Layout for Authenticated Users -->
    <mat-sidenav-container
      class="sidenav-container"
      *ngIf="currentUser && showMainLayout"
    >
      <mat-sidenav #sidenav mode="side" opened class="sidenav">
        <mat-nav-list>
          <!-- Admin Menu -->
          <div *ngIf="currentUser?.role === 'Admin'">
            <h3 mat-subheader>Quản trị viên</h3>
            <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
              <mat-icon matListItemIcon>dashboard</mat-icon>
              <span matListItemTitle>Tổng quan</span>
            </a>
            <a mat-list-item routerLink="/users" routerLinkActive="active">
              <mat-icon matListItemIcon>manage_accounts</mat-icon>
              <span matListItemTitle>Quản lý Người dùng</span>
            </a>
            <a mat-list-item routerLink="/books" routerLinkActive="active">
              <mat-icon matListItemIcon>book</mat-icon>
              <span matListItemTitle>Quản lý Sách</span>
            </a>
            <a mat-list-item routerLink="/categories" routerLinkActive="active">
              <mat-icon matListItemIcon>category</mat-icon>
              <span matListItemTitle>Quản lý Thể loại</span>
            </a>
            <a mat-list-item routerLink="/members" routerLinkActive="active">
              <mat-icon matListItemIcon>people</mat-icon>
              <span matListItemTitle>Quản lý Thành viên</span>
            </a>
            <a mat-list-item routerLink="/borrows" routerLinkActive="active">
              <mat-icon matListItemIcon>assignment</mat-icon>
              <span matListItemTitle>Quản lý Mượn/Trả</span>
            </a>
            <a mat-list-item routerLink="/shelves" routerLinkActive="active">
              <mat-icon matListItemIcon>inventory</mat-icon>
              <span matListItemTitle>Quản lý kệ sách</span>
            </a>
            <a mat-list-item [matMenuTriggerFor]="reportsMenu">
              <mat-icon matListItemIcon>assessment</mat-icon>
              <span matListItemTitle>Báo cáo</span>
              <mat-icon>arrow_drop_down</mat-icon>
            </a>
            <mat-menu #reportsMenu="matMenu">
              <a mat-menu-item routerLink="/reports/overdue-books">
                <mat-icon>warning</mat-icon>
                <span>Sách quá hạn</span>
              </a>
              <a mat-menu-item routerLink="/reports/fine-collection">
                <mat-icon>payments</mat-icon>
                <span>Thu phí phạt</span>
              </a>
              <a mat-menu-item routerLink="/reports/custom">
                <mat-icon>tune</mat-icon>
                <span>Báo cáo tùy chỉnh</span>
              </a>
            </mat-menu>
          </div>

          <!-- Librarian Menu -->
          <div *ngIf="currentUser?.role === 'Librarian'">
            <h3 mat-subheader>Thủ thư</h3>
            <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
              <mat-icon matListItemIcon>dashboard</mat-icon>
              <span matListItemTitle>Tổng quan</span>
            </a>
            <a mat-list-item routerLink="/books" routerLinkActive="active">
              <mat-icon matListItemIcon>book</mat-icon>
              <span matListItemTitle>Quản lý Sách</span>
            </a>
            <a mat-list-item routerLink="/categories" routerLinkActive="active">
              <mat-icon matListItemIcon>category</mat-icon>
              <span matListItemTitle>Quản lý Thể loại</span>
            </a>
            <a mat-list-item routerLink="/members" routerLinkActive="active">
              <mat-icon matListItemIcon>people</mat-icon>
              <span matListItemTitle>Quản lý Thành viên</span>
            </a>
            <a mat-list-item routerLink="/borrows" routerLinkActive="active">
              <mat-icon matListItemIcon>assignment</mat-icon>
              <span matListItemTitle>Quản lý Mượn/Trả</span>
            </a>
            <a mat-list-item routerLink="/shelves" routerLinkActive="active">
              <mat-icon matListItemIcon>inventory</mat-icon>
              <span matListItemTitle>Quản lý kệ sách</span>
            </a>
            <a mat-list-item [matMenuTriggerFor]="reportsMenu">
              <mat-icon matListItemIcon>assessment</mat-icon>
              <span matListItemTitle>Báo cáo</span>
              <mat-icon>arrow_drop_down</mat-icon>
            </a>
            <mat-menu #reportsMenu="matMenu">
              <a mat-menu-item routerLink="/reports/overdue-books">
                <mat-icon>warning</mat-icon>
                <span>Sách quá hạn</span>
              </a>
              <a mat-menu-item routerLink="/reports/fine-collection">
                <mat-icon>payments</mat-icon>
                <span>Thu phí phạt</span>
              </a>
              <a mat-menu-item routerLink="/reports/custom">
                <mat-icon>tune</mat-icon>
                <span>Báo cáo tùy chỉnh</span>
              </a>
            </mat-menu>
          </div>

          <!-- Assistant Menu -->
          <div *ngIf="currentUser?.role === 'Assistant'">
            <h3 mat-subheader>Trợ lý</h3>
            <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
              <mat-icon matListItemIcon>dashboard</mat-icon>
              <span matListItemTitle>Tổng quan</span>
            </a>
            <a mat-list-item routerLink="/borrows" routerLinkActive="active">
              <mat-icon matListItemIcon>assignment</mat-icon>
              <span matListItemTitle>Quản lý Mượn/Trả</span>
            </a>
            <a mat-list-item [matMenuTriggerFor]="reportsMenu">
              <mat-icon matListItemIcon>assessment</mat-icon>
              <span matListItemTitle>Báo cáo</span>
              <mat-icon>arrow_drop_down</mat-icon>
            </a>
            <mat-menu #reportsMenu="matMenu">
              <a mat-menu-item routerLink="/reports/overdue-books">
                <mat-icon>warning</mat-icon>
                <span>Sách quá hạn</span>
              </a>
              <a mat-menu-item routerLink="/reports/fine-collection">
                <mat-icon>payments</mat-icon>
                <span>Thu phí phạt</span>
              </a>
              <a mat-menu-item routerLink="/reports/custom">
                <mat-icon>tune</mat-icon>
                <span>Báo cáo tùy chỉnh</span>
              </a>
            </mat-menu>
          </div>
        </mat-nav-list>
      </mat-sidenav>

      <mat-sidenav-content class="main-content">
        <router-outlet></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>

    <!-- Auth Pages Layout -->
    <div class="auth-content" *ngIf="!currentUser && isAuthPage">
      <router-outlet></router-outlet>
    </div>

    <!-- Not Authenticated Component -->
    <app-not-authenticated
      *ngIf="!currentUser && !isAuthPage"
    ></app-not-authenticated>
  `,
  styles: [
    `
      .spacer {
        flex: 1 1 auto;
      }

      .sidenav-container {
        height: calc(100vh - 64px);
      }

      .sidenav {
        width: 250px;
      }

      .main-content {
        padding: 20px;
        height: 100%;
        overflow: auto;
      }

      .auth-content {
        height: calc(100vh - 64px);
        overflow: auto;
      }

      .active {
        background-color: rgba(255, 255, 255, 0.1);
      }

      mat-divider {
        margin: 8px 0;
      }
    `,
  ],
})
export class AppComponent implements OnInit {
  title = "Library Management System";
  currentUser: User | null = null;
  isAuthPage = false;
  showMainLayout = false;

  @ViewChild("sidenav") sidenav!: MatSidenav;

  constructor(
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user;
      this.updateLayoutState();
    });

    // Listen to route changes to determine if we're on auth pages
    this.router.events
      .pipe(
        filter(
          (event): event is NavigationEnd => event instanceof NavigationEnd
        )
      )
      .subscribe((event) => {
        this.isAuthPage = this.checkIfAuthPage(event.urlAfterRedirects);
        this.updateLayoutState();
      });

    // Initial check
    this.isAuthPage = this.checkIfAuthPage(this.router.url);
    this.updateLayoutState();
  }

  toggleSidenav(): void {
    if (this.sidenav) {
      this.sidenav.toggle();
    }
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(["/login"]);
  }

  private checkIfAuthPage(url: string): boolean {
    const authPages = [
      "/login",
      "/register",
      "/verify-email",
      "/reset-password",
      "/forgot-password",
    ];
    return authPages.some((page) => url.startsWith(page));
  }

  private updateLayoutState(): void {
    this.showMainLayout = !!this.currentUser && !this.isAuthPage;

    // Auto redirect authenticated users away from auth pages
    if (
      this.currentUser &&
      this.isAuthPage &&
      this.router.url !== "/verify-email"
    ) {
      this.router.navigate(["/books"]);
    }
  }

  showDebugInfo(): void {
    const tokenInfo = this.authService.getTokenInfo();
    const user = this.currentUser;

    let message = `Debug Info:\n`;
    message += `- Email: ${user?.email}\n`;
    message += `- EmailVerified in localStorage: ${user?.emailVerified}\n`;

    if (tokenInfo) {
      message += `- EmailVerified in JWT: ${tokenInfo.emailVerified}\n`;
      message += `- Token expired: ${tokenInfo.isExpired}\n`;
      message += `- Token claims: ${JSON.stringify(tokenInfo.claims, null, 2)}`;
    } else {
      message += `- Token: Không thể decode`;
    }

    // Copy to clipboard
    navigator.clipboard.writeText(message);

    this.snackBar.open(
      "Debug info đã copy vào clipboard! Vui lòng gửi cho developer.",
      "Đóng",
      {
        duration: 5000,
      }
    );

    console.log("Debug Info:", { user, tokenInfo });
  }

  resendVerificationEmail(): void {
    if (!this.currentUser?.email) {
      this.snackBar.open("Không tìm thấy email", "Đóng", { duration: 3000 });
      return;
    }

    this.authService
      .resendVerification({ email: this.currentUser.email })
      .subscribe({
        next: (response) => {
          this.snackBar.open(response.message, "Đóng", { duration: 5000 });
        },
        error: (error) => {
          const message =
            error.error?.message || "Không thể gửi lại email xác nhận";
          this.snackBar.open(message, "Đóng", { duration: 3000 });
        },
      });
  }

  syncWithToken(): void {
    this.authService.syncUserWithToken();
    this.snackBar.open("Đã sync với JWT token!", "Đóng", { duration: 3000 });
  }
}
