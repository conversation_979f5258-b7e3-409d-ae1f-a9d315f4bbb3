.dashboard-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  color: #3f51b5;
  font-weight: 500;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.error-container {
  color: #f44336;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 16px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-card mat-card-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #e8eaf6;
  margin-right: 16px;
}

.stat-icon mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #3f51b5;
}

.stat-info h2 {
  font-size: 28px;
  font-weight: 500;
  margin: 0;
  color: #333;
}

.stat-info p {
  margin: 4px 0 0;
  color: #666;
  font-size: 14px;
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.chart-card mat-card-content {
  height: 300px;
  padding: 16px;
}

/* Tables Section */
.tables-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.book-info {
  display: flex;
  align-items: center;
}

.book-thumbnail {
  width: 40px;
  height: 60px;
  object-fit: cover;
  margin-right: 12px;
  border-radius: 4px;
}

.book-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.book-author {
  font-size: 12px;
  color: #666;
}

/* Status styles */
.status-active {
  color: #4caf50;
  font-weight: 500;
}

.status-suspended {
  color: #ff9800;
  font-weight: 500;
}

.status-expired {
  color: #9e9e9e;
  font-weight: 500;
}

.status-banned {
  color: #f44336;
  font-weight: 500;
}

/* Reports Section */
.reports-section mat-card {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
}

.report-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
  text-decoration: none;
  color: #333;
  transition: background-color 0.3s ease, transform 0.3s ease;
  text-align: center;
}

.report-link:hover {
  background-color: #e8eaf6;
  transform: translateY(-5px);
}

.report-link mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  margin-bottom: 12px;
  color: #3f51b5;
}

.report-link span {
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .charts-section, .tables-section {
    grid-template-columns: 1fr;
  }
  
  .chart-card mat-card-content {
    height: 250px;
  }
}