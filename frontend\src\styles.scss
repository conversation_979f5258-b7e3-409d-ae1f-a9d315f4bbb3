/* You can add global styles to this file, and also import other style files */

html, body { 
  height: 100%; 
}

body { 
  margin: 0; 
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; 
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.spacer {
  flex: 1 1 auto;
}

.mat-mdc-card {
  margin: 16px;
}

.mat-mdc-form-field {
  margin: 8px 0;
}

.mat-mdc-table {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.success {
  color: #4caf50;
}

.warning {
  color: #ff9800;
}

.error {
  color: #f44336;
}

/* Snackbar styles */
.snackbar-success {
  background-color: #4caf50 !important;
  color: white !important;
}

.snackbar-error {
  background-color: #f44336 !important;
  color: white !important;
}

.snackbar-info {
  background-color: #2196f3 !important;
  color: white !important;
}

/* Form enhancements */
.mat-mdc-form-field.mat-form-field-appearance-outline {
  .mat-mdc-form-field-outline {
    border-radius: 12px;
  }
}

.mat-mdc-card {
  border-radius: 16px;
}

/* Material overrides for modern look */
.mat-mdc-raised-button {
  border-radius: 12px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
}

.mat-mdc-outlined-button, .mat-mdc-stroked-button {
  border-radius: 12px !important;
  font-weight: 500 !important;
  text-transform: none !important;
  border-width: 2px !important;
}

.mat-mdc-fab {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(20px)) {
  .blur-bg {
    backdrop-filter: blur(20px);
  }
}

/* Glass morphism effects */
.glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
} 