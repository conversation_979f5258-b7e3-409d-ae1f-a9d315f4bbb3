<div class="dashboard-container">
  <h1 class="page-title">B<PERSON>ng điều khiển</h1>

  <div *ngIf="isLoading" class="loading-container">
    <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    <p><PERSON><PERSON> tải dữ liệu...</p>
  </div>

  <div *ngIf="error" class="error-container">
    <p>{{ error }}</p>
    <button mat-raised-button color="primary" (click)="loadDashboardData()">Thử lại</button>
  </div>

  <div *ngIf="!isLoading && !error && stats" class="dashboard-content">
    <!-- Stats Cards -->
    <div class="stats-cards">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>menu_book</mat-icon>
          </div>
          <div class="stat-info">
            <h2>{{ stats.totalBooks }}</h2>
            <p>Tổng số sách</p>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>people</mat-icon>
          </div>
          <div class="stat-info">
            <h2>{{ stats.totalMembers }}</h2>
            <p>Tổng số thành viên</p>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>swap_horiz</mat-icon>
          </div>
          <div class="stat-info">
            <h2>{{ stats.activeBorrows }}</h2>
            <p>Đang mượn</p>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>warning</mat-icon>
          </div>
          <div class="stat-info">
            <h2>{{ stats.overdueBooks }}</h2>
            <p>Quá hạn</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>Thống kê hàng tháng</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <canvas baseChart
            [data]="monthlyBarChartData"
            [options]="barChartOptions"
            [type]="'bar'">
          </canvas>
        </mat-card-content>
      </mat-card>

      <mat-card class="chart-card">
        <mat-card-header>
          <mat-card-title>Thống kê theo danh mục</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <canvas baseChart
            [data]="categoryPieChartData"
            [options]="pieChartOptions"
            [type]="'pie'">
          </canvas>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Popular Books and Active Members -->
    <div class="tables-section">
      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>Sách phổ biến nhất</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <table mat-table [dataSource]="popularBooks" class="mat-elevation-z0">
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Tiêu đề</th>
              <td mat-cell *matCellDef="let book">
                <div class="book-info">
                  <img *ngIf="book.imageUrl" [src]="book.imageUrl" alt="{{ book.title }}" class="book-thumbnail">
                  <div>
                    <div class="book-title">{{ book.title }}</div>
                    <div class="book-author">{{ book.author }}</div>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Category Column -->
            <ng-container matColumnDef="category">
              <th mat-header-cell *matHeaderCellDef>Danh mục</th>
              <td mat-cell *matCellDef="let book">{{ book.categoryName }}</td>
            </ng-container>

            <!-- Borrow Count Column -->
            <ng-container matColumnDef="borrowCount">
              <th mat-header-cell *matHeaderCellDef>Số lượt mượn</th>
              <td mat-cell *matCellDef="let book">{{ book.borrowCount }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['title', 'category', 'borrowCount']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['title', 'category', 'borrowCount'];"></tr>
          </table>
        </mat-card-content>
      </mat-card>

      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>Thành viên tích cực nhất</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <table mat-table [dataSource]="activeMembers" class="mat-elevation-z0">
            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Tên</th>
              <td mat-cell *matCellDef="let member">{{ member.fullName }}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
              <td mat-cell *matCellDef="let member">
                <span [ngClass]="getMemberStatusClass(member.status)">
                  {{ getMemberStatusText(member.status) }}
                </span>
              </td>
            </ng-container>

            <!-- Borrow Count Column -->
            <ng-container matColumnDef="borrowCount">
              <th mat-header-cell *matHeaderCellDef>Số lượt mượn</th>
              <td mat-cell *matCellDef="let member">{{ member.borrowCount }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['name', 'status', 'borrowCount']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['name', 'status', 'borrowCount'];"></tr>
          </table>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Reports Section -->
    <div class="reports-section" *ngIf="canViewReports()">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Báo cáo</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="reports-grid">
            <a routerLink="/reports/overdue-books" class="report-link">
              <mat-icon>warning</mat-icon>
              <span>Sách quá hạn</span>
            </a>
            <a routerLink="/reports/fine-collection" class="report-link">
              <mat-icon>payments</mat-icon>
              <span>Thu phí phạt</span>
            </a>
            <a routerLink="/reports/custom" class="report-link">
              <mat-icon>analytics</mat-icon>
              <span>Báo cáo tùy chỉnh</span>
            </a>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>